# 宿舍入住管理系统

一个基于FastAPI + Vue 3的宿舍入住管理和费用分摊系统。

## 功能特性

- **部门管理**: 支持部门的增删改查操作
- **宿舍管理**: 宿舍信息和床位配置管理
- **住户管理**: 住户信息维护和部门关联
- **入住记录**: 入住和离开记录的完整管理
- **费用分摊**: 基于科学算法的自动费用分摊计算
- **统计报表**: 多维度统计分析和报表导出

## 技术架构

### 后端技术栈
- **FastAPI**: 现代化的Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **Pydantic**: 数据验证和序列化
- **SQLite/PostgreSQL**: 数据库存储
- **Loguru**: 日志管理

### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架
- **Composition API**: Vue 3组合式API
- **Pinia**: 状态管理
- **Tailwind CSS**: 实用优先的CSS框架
- **Axios**: HTTP客户端

## 项目结构

```
BedSharingCalc/
├── app/                     # 后端应用
│   ├── api/                 # API路由层
│   ├── core/                # 核心配置
│   ├── models/              # 数据模型
│   ├── schemas/             # Pydantic模式
│   ├── services/            # 业务服务层
│   ├── repositories/        # 数据访问层
│   ├── utils/               # 工具类
│   └── main.py              # 应用入口
├── frontend/                # 前端代码（待开发）
├── tests/                   # 测试代码
├── requirements.txt         # Python依赖
├── .env.example             # 环境变量示例
├── run.py                   # 启动脚本
└── README.md                # 项目说明
```

## 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+ (前端开发)

### 安装依赖

1. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

2. 安装Python依赖：
```bash
pip install -r requirements.txt
```

3. 配置环境变量：
```bash
cp .env.example .env
# 编辑.env文件，修改相关配置
```

### 运行应用

1. 启动后端服务：
```bash
python run.py
```

2. 访问应用：
- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- ReDoc文档: http://localhost:8000/redoc

## API接口

### 部门管理
- `GET /api/v1/departments/` - 获取部门列表
- `POST /api/v1/departments/` - 创建部门
- `GET /api/v1/departments/{id}` - 获取部门详情
- `PUT /api/v1/departments/{id}` - 更新部门
- `DELETE /api/v1/departments/{id}` - 删除部门

### 其他模块
- 宿舍管理 (开发中)
- 住户管理 (开发中)
- 入住记录 (开发中)
- 报表统计 (开发中)

## 数据库设计

系统采用关系型数据库设计，主要包含以下表：

- `departments` - 部门表
- `dormitories` - 宿舍表
- `residents` - 住户表
- `residence_records` - 入住记录表
- `monthly_reports` - 月度报告表
- `daily_allocations` - 日度分摊表

## 费用分摊算法

系统采用基于部门平均分摊的算法：

1. **有人入住场景**: 按入住部门数量平均分摊宿舍费用
2. **空宿舍场景**: 由公司承担100%费用
3. **混合场景**: 按实际情况动态计算分摊比例

## 开发计划

- [x] 项目架构设计
- [x] 后端基础框架
- [x] 数据模型设计
- [x] 部门管理API
- [ ] 宿舍管理API
- [ ] 住户管理API
- [ ] 入住记录API
- [ ] 费用分摊计算
- [ ] 报表统计API
- [ ] 前端界面开发
- [ ] 系统测试
- [ ] 部署文档

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
