# 应用配置
APP_NAME=宿舍入住管理系统
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./dormitory_management.db
DATABASE_ECHO=false

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8080"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 业务配置
UNIT_COST_PER_BED_DAY=100.0

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_EXTENSIONS=[".jpg", ".jpeg", ".png", ".pdf", ".xlsx", ".csv"]
