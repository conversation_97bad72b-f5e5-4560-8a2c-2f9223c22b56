# 宿舍入住管理系统 Makefile

.PHONY: help install run dev test clean init-db lint format

# 默认目标
help:
	@echo "宿舍入住管理系统 - 可用命令:"
	@echo "  install     安装依赖"
	@echo "  run         运行应用"
	@echo "  dev         开发模式运行"
	@echo "  test        运行测试"
	@echo "  init-db     初始化数据库"
	@echo "  lint        代码检查"
	@echo "  format      代码格式化"
	@echo "  clean       清理临时文件"

# 安装依赖
install:
	pip install -r requirements.txt

# 运行应用
run:
	python run.py

# 开发模式运行
dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 运行测试
test:
	pytest tests/ -v --cov=app

# 初始化数据库
init-db:
	python scripts/init_data.py

# 代码检查
lint:
	flake8 app/ --max-line-length=88 --extend-ignore=E203,W503
	black --check app/
	isort --check-only app/

# 代码格式化
format:
	black app/
	isort app/

# 清理临时文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	rm -f *.db
	rm -f test.db

# 创建虚拟环境
venv:
	python -m venv venv
	@echo "虚拟环境已创建，请运行以下命令激活:"
	@echo "source venv/bin/activate  # Linux/Mac"
	@echo "venv\\Scripts\\activate     # Windows"

# 生成requirements.txt
freeze:
	pip freeze > requirements.txt

# 检查依赖安全性
security:
	pip-audit

# 启动开发服务器（带自动重载）
serve:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-level info
